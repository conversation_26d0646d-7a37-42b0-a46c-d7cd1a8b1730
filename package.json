{"name": "steam-quiz", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@prisma/client": "^6.15.0", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "eslint": "^9", "eslint-config-next": "15.5.2", "prisma": "^6.15.0", "tailwindcss": "^4", "typescript": "^5"}}