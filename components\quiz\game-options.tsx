"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

interface GameOption {
  appid: string
  title: string
  header_image: string | null
}

interface GameOptionsProps {
  options: GameOption[]
  correctAnswerIndex: number
  onAnswer: (selectedIndex: number, isCorrect: boolean) => void
  showResult: boolean
  selectedIndex: number | null
}

export function GameOptions({
  options,
  correctAnswerIndex,
  onAnswer,
  showResult,
  selectedIndex
}: GameOptionsProps) {
  const [tempSelected, setTempSelected] = useState<number | null>(null)

  const handleSubmit = () => {
    if (tempSelected !== null) {
      const isCorrect = tempSelected === correctAnswerIndex
      onAnswer(tempSelected, isCorrect)
    }
  }

  const handleOptionClick = (index: number) => {
    if (!showResult) {
      setTempSelected(index)
    }
  }

  const getOptionStyle = (index: number) => {
    if (!showResult) {
      const isSelected = tempSelected === index
      return `cursor-pointer transition-all duration-200 hover:border-steam-accent-primary/50 hover:bg-steam-accent-primary/5 ${
        isSelected
          ? 'border-steam-accent-primary bg-steam-accent-primary/10'
          : 'border-steam-border'
      }`
    }

    if (index === correctAnswerIndex) {
      return "border-steam-success bg-steam-success/10"
    }

    if (selectedIndex === index && index !== correctAnswerIndex) {
      return "border-steam-error bg-steam-error/10"
    }

    return "opacity-60 border-steam-border"
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      <h3 className="text-xl font-semibold text-center mb-8 text-steam-text-primary">
        Which game does this review belong to?
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {options.map((option, index) => (
          <Card
            key={option.appid}
            className={`bg-steam-card border-2 ${getOptionStyle(index)}`}
            onClick={() => handleOptionClick(index)}
          >
            <CardContent className="p-4">
              <div className="flex items-center space-x-4">
                <div className="relative overflow-hidden rounded-lg">
                  {option.header_image ? (
                    <img
                      src={option.header_image}
                      alt={option.title}
                      className="w-20 h-28 object-cover transition-transform duration-200 hover:scale-105"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none'
                      }}
                    />
                  ) : (
                    <div className="w-20 h-28 bg-steam-bg-secondary rounded-lg flex items-center justify-center">
                      <span className="text-steam-text-secondary text-xs">No Image</span>
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-base leading-tight text-steam-text-primary mb-2">
                    {option.title}
                  </h4>

                  {showResult && (
                    <div className="flex items-center gap-2">
                      {index === correctAnswerIndex && (
                        <div className="text-steam-success font-medium text-sm flex items-center gap-1">
                          <span>✓</span> Correct Answer
                        </div>
                      )}

                      {selectedIndex === index && index !== correctAnswerIndex && (
                        <div className="text-steam-error font-medium text-sm flex items-center gap-1">
                          <span>✗</span> Your Answer
                        </div>
                      )}
                    </div>
                  )}
                </div>

                <div className="flex-shrink-0">
                  <div className={`w-4 h-4 rounded-full border-2 transition-colors ${
                    tempSelected === index
                      ? 'bg-steam-accent-primary border-steam-accent-primary'
                      : 'border-steam-border'
                  }`}>
                    {tempSelected === index && (
                      <div className="w-full h-full rounded-full bg-steam-bg-primary scale-50"></div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {!showResult && (
        <div className="flex justify-center pt-6">
          <Button
            onClick={handleSubmit}
            disabled={tempSelected === null}
            size="lg"
            className="px-8 bg-steam-accent-primary hover:bg-steam-accent-secondary text-steam-bg-primary font-semibold"
          >
            Submit Answer
          </Button>
        </div>
      )}
    </div>
  )
}
