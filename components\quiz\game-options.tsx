"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"

interface GameOption {
  appid: string
  title: string
  header_image: string | null
}

interface GameOptionsProps {
  options: GameOption[]
  correctAnswerIndex: number
  onAnswer: (selectedIndex: number, isCorrect: boolean) => void
  showResult: boolean
  selectedIndex: number | null
}

export function GameOptions({ 
  options, 
  correctAnswerIndex, 
  onAnswer, 
  showResult, 
  selectedIndex 
}: GameOptionsProps) {
  const [tempSelected, setTempSelected] = useState<string>("")

  const handleSubmit = () => {
    const selectedIdx = parseInt(tempSelected)
    const isCorrect = selectedIdx === correctAnswerIndex
    onAnswer(selectedIdx, isCorrect)
  }

  const getOptionStyle = (index: number) => {
    if (!showResult) {
      return "hover:bg-accent cursor-pointer transition-colors"
    }

    if (index === correctAnswerIndex) {
      return "bg-green-100 border-green-500 dark:bg-green-900/20 dark:border-green-400"
    }

    if (selectedIndex === index && index !== correctAnswerIndex) {
      return "bg-red-100 border-red-500 dark:bg-red-900/20 dark:border-red-400"
    }

    return "opacity-60"
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-4">
      <h3 className="text-xl font-semibold text-center mb-6">
        Which game does this review belong to?
      </h3>
      
      <RadioGroup 
        value={tempSelected} 
        onValueChange={setTempSelected}
        disabled={showResult}
        className="space-y-3"
      >
        {options.map((option, index) => (
          <div key={option.appid} className="relative">
            <Card className={`transition-all duration-200 ${getOptionStyle(index)}`}>
              <CardContent className="p-4">
                <div className="flex items-center space-x-4">
                  <RadioGroupItem 
                    value={index.toString()} 
                    id={`option-${index}`}
                    className="mt-1"
                  />
                  <Label 
                    htmlFor={`option-${index}`} 
                    className="flex items-center space-x-4 cursor-pointer flex-1"
                  >
                    {option.header_image && (
                      <img
                        src={option.header_image}
                        alt={option.title}
                        className="w-16 h-24 object-cover rounded"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none'
                        }}
                      />
                    )}
                    <div className="flex-1">
                      <h4 className="font-medium text-base leading-tight">
                        {option.title}
                      </h4>
                    </div>
                  </Label>
                  
                  {showResult && index === correctAnswerIndex && (
                    <div className="text-green-600 dark:text-green-400 font-medium">
                      ✓ Correct Answer
                    </div>
                  )}
                  
                  {showResult && selectedIndex === index && index !== correctAnswerIndex && (
                    <div className="text-red-600 dark:text-red-400 font-medium">
                      ✗ Your Answer
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        ))}
      </RadioGroup>

      {!showResult && (
        <div className="flex justify-center pt-4">
          <Button 
            onClick={handleSubmit}
            disabled={!tempSelected}
            size="lg"
            className="px-8"
          >
            Submit Answer
          </Button>
        </div>
      )}
    </div>
  )
}
