"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

interface GameOption {
  appid: string
  title: string
  header_image: string | null
}

interface GameOptionsProps {
  options: GameOption[]
  correctAnswerIndex: number
  onAnswer: (selectedIndex: number, isCorrect: boolean) => void
  showResult: boolean
  selectedIndex: number | null
}

export function GameOptions({
  options,
  correctAnswerIndex,
  onAnswer,
  showResult,
  selectedIndex
}: GameOptionsProps) {
  const [tempSelected, setTempSelected] = useState<number | null>(null)

  const handleSubmit = () => {
    if (tempSelected !== null) {
      const isCorrect = tempSelected === correctAnswerIndex
      onAnswer(tempSelected, isCorrect)
    }
  }

  const handleOptionClick = (index: number) => {
    if (!showResult) {
      setTempSelected(index)
    }
  }

  const getOptionStyle = (index: number) => {
    if (!showResult) {
      const isSelected = tempSelected === index
      return `cursor-pointer transition-all duration-300 hover:border-steam-accent-primary/70 hover:shadow-lg hover:shadow-steam-accent-primary/20 ${
        isSelected
          ? 'border-steam-accent-primary shadow-lg shadow-steam-accent-primary/30 transform scale-[1.02]'
          : 'border-steam-border hover:transform hover:scale-[1.01]'
      }`
    }

    if (index === correctAnswerIndex) {
      return "border-steam-success shadow-lg shadow-steam-success/30"
    }

    if (selectedIndex === index && index !== correctAnswerIndex) {
      return "border-steam-error shadow-lg shadow-steam-error/30"
    }

    return "opacity-60 border-steam-border"
  }

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      <h3 className="text-xl font-semibold text-center mb-8 text-steam-text-primary">
        Which game does this review belong to?
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {options.map((option, index) => (
          <Card
            key={option.appid}
            className={`bg-steam-card border-2 ${getOptionStyle(index)} overflow-hidden group`}
            onClick={() => handleOptionClick(index)}
          >
            <div className="relative">
              {/* Game Header Image */}
              <div className="relative overflow-hidden bg-steam-bg-secondary">
                {option.header_image ? (
                  <img
                    src={option.header_image}
                    alt={option.title}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                ) : (
                  <div className="w-full h-48 bg-steam-bg-secondary flex items-center justify-center">
                    <span className="text-steam-text-secondary text-sm">No Image Available</span>
                  </div>
                )}

                {/* Selection Indicator */}
                <div className="absolute top-3 right-3">
                  <div className={`w-6 h-6 rounded-full border-2 transition-all duration-200 ${
                    tempSelected === index
                      ? 'bg-steam-accent-primary border-steam-accent-primary shadow-lg'
                      : 'bg-steam-card/80 border-steam-border backdrop-blur-sm'
                  }`}>
                    {tempSelected === index && (
                      <div className="w-full h-full rounded-full bg-steam-bg-primary scale-50 flex items-center justify-center">
                        <div className="w-2 h-2 rounded-full bg-steam-accent-primary"></div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Result Badges */}
                {showResult && (
                  <div className="absolute top-3 left-3">
                    {index === correctAnswerIndex && (
                      <div className="bg-steam-success/90 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1 backdrop-blur-sm">
                        <span>✓</span> Correct
                      </div>
                    )}

                    {selectedIndex === index && index !== correctAnswerIndex && (
                      <div className="bg-steam-error/90 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1 backdrop-blur-sm">
                        <span>✗</span> Wrong
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Game Title */}
              <CardContent className="p-4">
                <h4 className="font-medium text-base leading-tight text-steam-text-primary text-center">
                  {option.title}
                </h4>
              </CardContent>
            </div>
          </Card>
        ))}
      </div>

      {!showResult && (
        <div className="flex justify-center pt-6">
          <Button
            onClick={handleSubmit}
            disabled={tempSelected === null}
            size="lg"
            className="px-8 bg-steam-accent-primary hover:bg-steam-accent-secondary text-steam-bg-primary font-semibold"
          >
            Submit Answer
          </Button>
        </div>
      )}
    </div>
  )
}
