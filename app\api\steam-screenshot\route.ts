import { NextRequest, NextResponse } from 'next/server'

interface SteamScreenshotResponse {
  success: number
  data: {
    screenshots: Array<{
      id: number
      path_thumbnail: string
      path_full: string
    }>
  }
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const appid = searchParams.get('appid')

  if (!appid) {
    return NextResponse.json(
      { error: 'App ID is required' },
      { status: 400 }
    )
  }

  try {
    // Fetch screenshots from Steam API
    const steamUrl = `https://store.steampowered.com/api/appdetails?appids=${appid}&filters=screenshots`
    
    const response = await fetch(steamUrl, {
      headers: {
        'User-Agent': 'Steam Quiz App'
      }
    })

    if (!response.ok) {
      throw new Error(`Steam API responded with status: ${response.status}`)
    }

    const data = await response.json()
    const gameData = data[appid]

    if (!gameData || !gameData.success) {
      throw new Error('Steam API returned unsuccessful response')
    }

    const screenshots = gameData.data?.screenshots
    if (!screenshots || screenshots.length === 0) {
      return NextResponse.json(
        { error: 'No screenshots found for this game' },
        { status: 404 }
      )
    }

    // Select a random screenshot
    const randomScreenshot = screenshots[Math.floor(Math.random() * screenshots.length)]

    return NextResponse.json({
      screenshot_url: randomScreenshot.path_full,
      thumbnail_url: randomScreenshot.path_thumbnail
    })

  } catch (error) {
    console.error('Error fetching Steam screenshot:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch screenshot from Steam',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
