import { prisma } from './prisma'

export interface QuizGame {
  appid: bigint
  title: string
  header_image: string | null
}

export interface QuizQuestion {
  correctGame: QuizGame
  options: QuizGame[]
  review: string
}

/**
 * Get a random game from the database that has reviews
 */
export async function getRandomGameWithReviews(): Promise<QuizGame | null> {
  try {
    // Get total count of games with reviews
    const totalGames = await prisma.games.count({
      where: {
        review_count: {
          gt: 0
        }
      }
    })

    if (totalGames === 0) {
      return null
    }

    // Get a random offset
    const randomOffset = Math.floor(Math.random() * totalGames)

    // Fetch the random game
    const game = await prisma.games.findFirst({
      where: {
        review_count: {
          gt: 0
        }
      },
      skip: randomOffset,
      select: {
        appid: true,
        title: true,
        header_image: true
      }
    })

    return game
  } catch (error) {
    console.error('Error fetching random game:', error)
    return null
  }
}

/**
 * Get alternative games from the same genres as the correct game
 */
export async function getAlternativeGames(correctGameAppid: bigint, count: number = 3): Promise<QuizGame[]> {
  try {
    // First, get the genres of the correct game
    const correctGameGenres = await prisma.game_genres.findMany({
      where: {
        appid: correctGameAppid
      },
      select: {
        genre_id: true
      }
    })

    if (correctGameGenres.length === 0) {
      // If no genres found, get random games
      return getRandomAlternatives(correctGameAppid, count)
    }

    const genreIds = correctGameGenres.map(g => g.genre_id)

    // Get games from the same genres, excluding the correct game
    const alternatives = await prisma.games.findMany({
      where: {
        appid: {
          not: correctGameAppid
        },
        genres: {
          some: {
            genre_id: {
              in: genreIds
            }
          }
        },
        review_count: {
          gt: 0
        }
      },
      select: {
        appid: true,
        title: true,
        header_image: true
      },
      take: count * 2 // Take more than needed to have options
    })

    // Shuffle and return the requested count
    const shuffled = alternatives.sort(() => Math.random() - 0.5)
    return shuffled.slice(0, count)
  } catch (error) {
    console.error('Error fetching alternative games:', error)
    return getRandomAlternatives(correctGameAppid, count)
  }
}

/**
 * Get random alternative games when genre-based selection fails
 */
async function getRandomAlternatives(excludeAppid: bigint, count: number): Promise<QuizGame[]> {
  try {
    const alternatives = await prisma.games.findMany({
      where: {
        appid: {
          not: excludeAppid
        },
        review_count: {
          gt: 0
        }
      },
      select: {
        appid: true,
        title: true,
        header_image: true
      },
      take: count * 3 // Take more to have options for shuffling
    })

    // Shuffle and return the requested count
    const shuffled = alternatives.sort(() => Math.random() - 0.5)
    return shuffled.slice(0, count)
  } catch (error) {
    console.error('Error fetching random alternatives:', error)
    return []
  }
}

/**
 * Generate a complete quiz question with correct answer and alternatives
 */
export async function generateQuizQuestion(): Promise<{ correctGame: QuizGame; alternatives: QuizGame[] } | null> {
  try {
    // Get a random game as the correct answer
    const correctGame = await getRandomGameWithReviews()
    if (!correctGame) {
      return null
    }

    // Get alternative games
    const alternatives = await getAlternativeGames(correctGame.appid, 3)
    if (alternatives.length < 3) {
      console.warn('Could not get enough alternative games')
      return null
    }

    return {
      correctGame,
      alternatives
    }
  } catch (error) {
    console.error('Error generating quiz question:', error)
    return null
  }
}
