generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Games {
  appid             BigInt   @id @db.BigInt
  title             String
  header_image      String?
  capsule_image     String?
  release_date      DateTime?
  is_free           Boolean?
  price_final       Int?           // centesimi
  price_currency    String?
  review_count      Int
  review_score_desc String?
  metacritic_score  Int?
  platforms         Json?
  developers        String[]
  publishers        String[]
  raw_appdetails    Json?
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt
  genres            Game_genres[]
}

model Genres {
  id    Int    @id @default(autoincrement())
  label String @unique
  games Game_genres[]
}

model Game_genres {
  appid    BigInt
  genre_id Int
  game     Games  @relation(fields: [appid], references: [appid], onDelete: Cascade)
  genre    Genres @relation(fields: [genre_id], references: [id], onDelete: Cascade)

  @@id([appid, genre_id])
  @@index([genre_id])
}
