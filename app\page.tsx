import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { TopBar } from "@/components/layout/top-bar"

export default function Home() {
  return (
    <div className="min-h-screen bg-steam-bg-primary">
      <TopBar />
      <div className="bg-steam-bg-secondary/30 min-h-[calc(100vh-80px)]">
        <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h1 className="text-6xl font-bold mb-4 text-steam-text-primary">
            Steam Quiz
          </h1>
          <p className="text-xl text-steam-text-secondary max-w-2xl mx-auto">
            Test your gaming knowledge! Read real Steam reviews and guess which game they're about.
          </p>
        </div>

        <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8 mb-12">
          <Card className="bg-steam-card border-steam-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-steam-text-primary">
                🎮 How to Play
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-steam-text-secondary">
                <li>• Read a real Steam review from a random game</li>
                <li>• Choose from 4 game options</li>
                <li>• Build your streak and improve your score</li>
                <li>• Learn about new games along the way!</li>
              </ul>
            </CardContent>
          </Card>

          <Card className="bg-steam-card border-steam-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-steam-text-primary">
                📊 Features
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-steam-text-secondary">
                <li>• Real Steam reviews from actual players</li>
                <li>• Smart genre-based game alternatives</li>
                <li>• Score tracking and streak system</li>
                <li>• Responsive design for all devices</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        <div className="text-center space-y-6">
          <Link href="/quiz">
            <Button size="lg" className="text-lg px-8 py-6 h-auto bg-steam-accent-primary hover:bg-steam-accent-secondary text-steam-bg-primary font-semibold">
              🚀 Start Quiz
            </Button>
          </Link>

          <div className="flex justify-center gap-4">
            <Link href="/test-db">
              <Button variant="outline" size="sm" className="border-steam-border text-steam-text-secondary hover:bg-steam-card">
                Test Database
              </Button>
            </Link>
          </div>
        </div>

        <div className="mt-16 text-center">
          <Card className="max-w-2xl mx-auto bg-steam-card border-steam-border">
            <CardHeader>
              <CardTitle className="text-steam-text-primary">About This Project</CardTitle>
              <CardDescription className="text-steam-text-secondary">
                Built with Next.js, Tailwind CSS, shadcn/ui, and Neon PostgreSQL
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-steam-text-secondary">
                This quiz application uses real Steam game data and reviews to create an engaging
                guessing game. Test your knowledge of the Steam gaming library!
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
      </div>
    </div>
  )
}
