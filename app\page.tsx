import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h1 className="text-6xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Steam Quiz
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Test your gaming knowledge! Read real Steam reviews and guess which game they're about.
          </p>
        </div>

        <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8 mb-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🎮 How to Play
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• Read a real Steam review from a random game</li>
                <li>• Choose from 4 game options</li>
                <li>• Build your streak and improve your score</li>
                <li>• Learn about new games along the way!</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                📊 Features
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li>• Real Steam reviews from actual players</li>
                <li>• Smart genre-based game alternatives</li>
                <li>• Score tracking and streak system</li>
                <li>• Responsive design for all devices</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        <div className="text-center space-y-6">
          <Link href="/quiz">
            <Button size="lg" className="text-lg px-8 py-6 h-auto">
              🚀 Start Quiz
            </Button>
          </Link>

          <div className="flex justify-center gap-4">
            <Link href="/test-db">
              <Button variant="outline" size="sm">
                Test Database
              </Button>
            </Link>
          </div>
        </div>

        <div className="mt-16 text-center">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>About This Project</CardTitle>
              <CardDescription>
                Built with Next.js, Tailwind CSS, shadcn/ui, and Neon PostgreSQL
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This quiz application uses real Steam game data and reviews to create an engaging
                guessing game. Test your knowledge of the Steam gaming library!
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
