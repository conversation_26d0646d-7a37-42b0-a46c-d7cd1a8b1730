@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  /* Steam Color Palette */
  --steam-bg-primary: #171a21;
  --steam-bg-secondary: #1b2838;
  --steam-card: #2a475e;
  --steam-accent-primary: #66c0f4;
  --steam-accent-secondary: #1999ff;
  --steam-success: #4CAF50;
  --steam-error: #d9534f;
  --steam-text-primary: #c7d5e0;
  --steam-text-secondary: #8f98a0;
  --steam-border: #2c3e50;

  /* Theme Variables */
  --background: var(--steam-bg-primary);
  --foreground: var(--steam-text-primary);
}

@theme inline {
  /* Colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-steam-bg-primary: var(--steam-bg-primary);
  --color-steam-bg-secondary: var(--steam-bg-secondary);
  --color-steam-card: var(--steam-card);
  --color-steam-accent-primary: var(--steam-accent-primary);
  --color-steam-accent-secondary: var(--steam-accent-secondary);
  --color-steam-success: var(--steam-success);
  --color-steam-error: var(--steam-error);
  --color-steam-text-primary: var(--steam-text-primary);
  --color-steam-text-secondary: var(--steam-text-secondary);
  --color-steam-border: var(--steam-border);

  /* Fonts */
  --font-sans: "Inter", "Motiva Sans", Arial, "Helvetica Neue", sans-serif;
  --font-mono: "Courier New", monospace;

  /* Custom utilities */
  --color-primary: var(--steam-accent-primary);
  --color-secondary: var(--steam-accent-secondary);
  --color-success: var(--steam-success);
  --color-destructive: var(--steam-error);
  --color-muted: var(--steam-text-secondary);
  --color-card: var(--steam-card);
  --color-border: var(--steam-border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  min-height: 100vh;
}
