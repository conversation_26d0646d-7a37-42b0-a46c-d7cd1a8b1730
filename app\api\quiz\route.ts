import { NextResponse } from 'next/server'
import { generateQuizQuestion } from '@/lib/quiz-service'

interface SteamReviewResponse {
  review: string
  author_playtime: number
  recommended: boolean
  helpful_votes: number
  funny_votes: number
  review_date: string
  author_reviews: number
  author_games: number
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const language = searchParams.get('language') || 'english'
  try {
    // Generate quiz question with correct game and alternatives
    const quizData = await generateQuizQuestion()
    
    if (!quizData) {
      return NextResponse.json(
        { error: 'Unable to generate quiz question' },
        { status: 500 }
      )
    }

    const { correctGame, alternatives } = quizData

    // Fetch a review for the correct game
    let reviewData: SteamReviewResponse | null = null
    let retryCount = 0
    const maxRetries = 3

    while (!reviewData && retryCount < maxRetries) {
      try {
        const reviewResponse = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/steam-reviews?appid=${correctGame.appid}&language=${language}`,
          {
            cache: 'no-store',
            headers: {
              'User-Agent': 'Steam Quiz App'
            }
          }
        )

        if (reviewResponse.ok) {
          reviewData = await reviewResponse.json()
        } else {
          console.warn(`Failed to fetch review for game ${correctGame.title} (attempt ${retryCount + 1})`)
        }
      } catch (error) {
        console.error(`Error fetching review for game ${correctGame.title}:`, error)
      }
      
      retryCount++
    }

    if (!reviewData) {
      return NextResponse.json(
        { error: 'Unable to fetch review for the selected game' },
        { status: 500 }
      )
    }

    // Combine all options and shuffle them
    const allOptions = [correctGame, ...alternatives]
    const shuffledOptions = allOptions.sort(() => Math.random() - 0.5)

    // Find the index of the correct answer in the shuffled array
    const correctAnswerIndex = shuffledOptions.findIndex(
      option => option.appid === correctGame.appid
    )

    return NextResponse.json({
      question: {
        review: reviewData.review,
        author_playtime: reviewData.author_playtime,
        recommended: reviewData.recommended,
        helpful_votes: reviewData.helpful_votes,
        funny_votes: reviewData.funny_votes,
        review_date: reviewData.review_date,
        author_reviews: reviewData.author_reviews,
        author_games: reviewData.author_games
      },
      options: shuffledOptions.map(game => ({
        appid: game.appid.toString(),
        title: game.title,
        header_image: game.header_image
      })),
      correctAnswerIndex,
      correctGame: {
        appid: correctGame.appid.toString(),
        title: correctGame.title,
        header_image: correctGame.header_image
      }
    })

  } catch (error) {
    console.error('Error generating quiz:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to generate quiz question',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
