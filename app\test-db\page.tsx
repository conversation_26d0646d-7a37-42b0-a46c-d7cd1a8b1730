"use client"
import React, { useState } from "react"
import { testDbConnection } from "@/lib/db-test"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardContent, CardFooter, CardDescription } from "@/components/ui/card"

export default function TestDbPage() {
  const [result, setResult] = useState<null | { success: boolean; count?: number; error?: string }>(null)
  const [loading, setLoading] = useState(false)

  async function handleTest() {
    setLoading(true)
    setResult(null)
    try {
      const res = await testDbConnection()
      setResult(res)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Database Connection Test</CardTitle>
          <CardDescription>
            Click the button below to test the connection to your PostgreSQL database using Prisma.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {result === null ? (
            <div className="text-muted-foreground">No test run yet.</div>
          ) : result.success ? (
            <div className="text-green-600 font-medium">Success! Games count: {result.count}</div>
          ) : (
            <div className="text-red-600 font-medium">Error: {result.error}</div>
          )}
        </CardContent>
        <CardFooter>
          <Button onClick={handleTest} disabled={loading}>
            {loading ? "Testing..." : "Test Connection"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
