import { NextRequest, NextResponse } from 'next/server'

interface SteamReview {
  recommendationid: string
  author: {
    steamid: string
    num_games_owned: number
    num_reviews: number
    playtime_forever: number
    playtime_last_two_weeks: number
    playtime_at_review: number
    last_played: number
  }
  language: string
  review: string
  timestamp_created: number
  timestamp_updated: number
  voted_up: boolean
  votes_up: number
  votes_funny: number
  weighted_vote_score: string
  comment_count: number
  steam_purchase: boolean
  received_for_free: boolean
  written_during_early_access: boolean
}

interface SteamReviewsResponse {
  success: number
  query_summary: {
    num_reviews: number
    review_score: number
    review_score_desc: string
    total_positive: number
    total_negative: number
    total_reviews: number
  }
  reviews: SteamReview[]
  cursor: string
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const appid = searchParams.get('appid')
  const language = searchParams.get('language') || 'english'
  const excludeReview = searchParams.get('exclude') // Review text to exclude
  const gameName = searchParams.get('gameName') // Game name to censor

  if (!appid) {
    return NextResponse.json(
      { error: 'App ID is required' },
      { status: 400 }
    )
  }

  try {
    // Fetch reviews from Steam API with language support
    const steamUrl = `https://store.steampowered.com/appreviews/${appid}?json=1&language=${language}&review_type=all&num_per_page=50&cursor=*`

    const response = await fetch(steamUrl, {
      headers: {
        'User-Agent': 'Steam Quiz App'
      }
    })

    if (!response.ok) {
      throw new Error(`Steam API responded with status: ${response.status}`)
    }

    const data: SteamReviewsResponse = await response.json()

    if (data.success !== 1) {
      throw new Error('Steam API returned unsuccessful response')
    }

    if (!data.reviews || data.reviews.length === 0) {
      return NextResponse.json(
        { error: 'No reviews found for this game' },
        { status: 404 }
      )
    }

    // Count words in review text
    const countWords = (text: string) => {
      return text.trim().split(/\s+/).length
    }

    // Censor game name in review text
    const censorGameName = (reviewText: string, gameTitle?: string) => {
      if (!gameTitle) return reviewText

      // Create variations of the game name to catch different formats
      const gameNameVariations = [
        gameTitle,
        gameTitle.toLowerCase(),
        gameTitle.toUpperCase(),
        // Remove common suffixes like "2", "II", "3", etc.
        gameTitle.replace(/\s+(2|II|3|III|4|IV|5|V|\d+)$/i, ''),
        // Remove subtitles (text after colon or dash)
        gameTitle.split(':')[0].trim(),
        gameTitle.split('-')[0].trim(),
        // Remove "The" prefix
        gameTitle.replace(/^The\s+/i, ''),
      ]

      let censoredText = reviewText

      gameNameVariations.forEach(variation => {
        if (variation && variation.length > 2) { // Only censor names longer than 2 characters
          // Create regex that matches the game name with word boundaries
          const regex = new RegExp(`\\b${variation.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi')
          censoredText = censoredText.replace(regex, '[GAME NAME]')
        }
      })

      return censoredText
    }

    // Filter reviews to get good quality ones with priority for funny reviews
    const qualityReviews = data.reviews.filter(review => {
      const wordCount = countWords(review.review)
      return (
        wordCount >= 50 && // At least 50 words
        review.review.length <= 2000 && // Not too long
        review.author.playtime_forever > 30 && // At least 30 minutes played
        review.language === language && // Match requested language
        !review.review.toLowerCase().includes('refund') && // Avoid refund-focused reviews
        review.votes_up >= 0 && // Has some positive votes or neutral
        (!excludeReview || review.review !== excludeReview) // Exclude specific review if provided
      )
    })

    // Sort by funny votes first, then by helpful votes
    const sortedReviews = qualityReviews.sort((a, b) => {
      // Prioritize funny reviews
      if (b.votes_funny !== a.votes_funny) {
        return b.votes_funny - a.votes_funny
      }
      // Then by helpful votes
      return b.votes_up - a.votes_up
    })

    if (sortedReviews.length === 0) {
      // If no quality reviews in requested language, try English as fallback
      let fallbackReview = data.reviews.find(review => {
        const wordCount = countWords(review.review)
        return wordCount >= 20 && review.language === language
      })

      // If still no review found and language is not English, try English
      if (!fallbackReview && language !== 'english') {
        console.log(`No reviews found in ${language}, trying English fallback`)

        // Fetch English reviews as fallback
        try {
          const englishUrl = `https://store.steampowered.com/appreviews/${appid}?json=1&language=english&review_type=all&num_per_page=50&cursor=*`
          const englishResponse = await fetch(englishUrl, {
            headers: {
              'User-Agent': 'Steam Quiz App'
            }
          })

          if (englishResponse.ok) {
            const englishData = await englishResponse.json()
            if (englishData.success === 1 && englishData.reviews) {
              // Apply same quality filters for English reviews
              const englishQualityReviews = englishData.reviews.filter((review: any) => {
                const wordCount = countWords(review.review)
                return (
                  wordCount >= 50 &&
                  review.review.length <= 2000 &&
                  review.author.playtime_forever > 30 &&
                  review.language === 'english' &&
                  !review.review.toLowerCase().includes('refund') &&
                  review.votes_up >= 0 &&
                  (!excludeReview || review.review !== excludeReview)
                )
              })

              if (englishQualityReviews.length > 0) {
                // Sort English reviews by funny votes too
                const sortedEnglishReviews = englishQualityReviews.sort((a: any, b: any) => {
                  if (b.votes_funny !== a.votes_funny) {
                    return b.votes_funny - a.votes_funny
                  }
                  return b.votes_up - a.votes_up
                })

                fallbackReview = sortedEnglishReviews[0]
              } else {
                // If no quality English reviews, try any English review
                fallbackReview = englishData.reviews.find((review: any) => {
                  const wordCount = countWords(review.review)
                  return wordCount >= 20 && review.language === 'english'
                })
              }
            }
          }
        } catch (englishError) {
          console.error('Error fetching English fallback reviews:', englishError)
        }
      }

      if (!fallbackReview) {
        return NextResponse.json(
          { error: 'No suitable reviews found for this game in any supported language' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        review: censorGameName(fallbackReview.review, gameName),
        author_playtime: fallbackReview.author.playtime_forever,
        recommended: fallbackReview.voted_up,
        helpful_votes: fallbackReview.votes_up,
        funny_votes: fallbackReview.votes_funny,
        review_date: new Date(fallbackReview.timestamp_created * 1000).toLocaleDateString(),
        author_reviews: fallbackReview.author.num_reviews,
        author_games: fallbackReview.author.num_games_owned
      })
    }

    // Select the best review (funny reviews have priority due to sorting)
    const selectedReview = sortedReviews[0]

    return NextResponse.json({
      review: censorGameName(selectedReview.review, gameName),
      author_playtime: selectedReview.author.playtime_forever,
      recommended: selectedReview.voted_up,
      helpful_votes: selectedReview.votes_up,
      funny_votes: selectedReview.votes_funny,
      review_date: new Date(selectedReview.timestamp_created * 1000).toLocaleDateString(),
      author_reviews: selectedReview.author.num_reviews,
      author_games: selectedReview.author.num_games_owned
    })

  } catch (error) {
    console.error('Error fetching Steam reviews:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch reviews from Steam',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
