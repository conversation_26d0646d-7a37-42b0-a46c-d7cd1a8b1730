import { NextRequest, NextResponse } from 'next/server'

interface SteamGameDetailsResponse {
  success: number
  data: {
    name: string
    release_date: {
      coming_soon: boolean
      date: string
    }
    price_overview?: {
      currency: string
      initial: number
      final: number
      discount_percent: number
      initial_formatted: string
      final_formatted: string
    }
    is_free: boolean
    genres: Array<{
      id: string
      description: string
    }>
    developers: string[]
    publishers: string[]
  }
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const appid = searchParams.get('appid')

  if (!appid) {
    return NextResponse.json(
      { error: 'App ID is required' },
      { status: 400 }
    )
  }

  try {
    // Fetch game details from Steam API
    const steamUrl = `https://store.steampowered.com/api/appdetails?appids=${appid}&filters=basic,price_overview,release_date,genres,developers,publishers`
    
    const response = await fetch(steamUrl, {
      headers: {
        'User-Agent': 'Steam Quiz App'
      }
    })

    if (!response.ok) {
      throw new Error(`Steam API responded with status: ${response.status}`)
    }

    const data = await response.json()
    const gameData = data[appid]

    if (!gameData || !gameData.success) {
      throw new Error('Steam API returned unsuccessful response')
    }

    const gameInfo = gameData.data

    // Extract year from release date
    let releaseYear = 'Unknown'
    if (gameInfo.release_date && gameInfo.release_date.date) {
      try {
        const releaseDate = new Date(gameInfo.release_date.date)
        if (!isNaN(releaseDate.getTime())) {
          releaseYear = releaseDate.getFullYear().toString()
        }
      } catch (e) {
        // Try to extract year from string format
        const yearMatch = gameInfo.release_date.date.match(/\d{4}/)
        if (yearMatch) {
          releaseYear = yearMatch[0]
        }
      }
    }

    // Format price information
    let priceInfo = 'Free to Play'
    if (!gameInfo.is_free && gameInfo.price_overview) {
      priceInfo = gameInfo.price_overview.final_formatted
      if (gameInfo.price_overview.discount_percent > 0) {
        priceInfo = `${gameInfo.price_overview.final_formatted} (${gameInfo.price_overview.discount_percent}% off)`
      }
    }

    return NextResponse.json({
      name: gameInfo.name,
      release_year: releaseYear,
      release_date: gameInfo.release_date?.date || 'Unknown',
      price: priceInfo,
      is_free: gameInfo.is_free,
      genres: gameInfo.genres?.map(g => g.description) || [],
      developers: gameInfo.developers || [],
      publishers: gameInfo.publishers || []
    })

  } catch (error) {
    console.error('Error fetching Steam game details:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch game details from Steam',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
