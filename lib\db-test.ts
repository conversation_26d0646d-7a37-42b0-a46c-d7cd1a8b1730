import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

export async function testDbConnection() {
  try {
    // Try a simple query: count the number of games
    const count = await prisma.games.count()
    return { success: true, count }
  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : String(error) }
  } finally {
    await prisma.$disconnect()
  }
}
