"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ReviewDisplay } from "@/components/quiz/review-display"
import { GameOptions } from "@/components/quiz/game-options"
import { ScoreDisplay } from "@/components/quiz/score-display"

interface QuizQuestion {
  question: {
    review: string
    author_playtime: number
    recommended: boolean
    helpful_votes: number
  }
  options: Array<{
    appid: string
    title: string
    header_image: string | null
  }>
  correctAnswerIndex: number
  correctGame: {
    appid: string
    title: string
    header_image: string | null
  }
}

type GameState = 'loading' | 'playing' | 'answered' | 'error'

export default function QuizPage() {
  const [gameState, setGameState] = useState<GameState>('loading')
  const [currentQuestion, setCurrentQuestion] = useState<QuizQuestion | null>(null)
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null)
  const [score, setScore] = useState(0)
  const [totalQuestions, setTotalQuestions] = useState(0)
  const [streak, setStreak] = useState(0)
  const [error, setError] = useState<string | null>(null)

  const fetchNewQuestion = async () => {
    setGameState('loading')
    setError(null)
    
    try {
      const response = await fetch('/api/quiz', { cache: 'no-store' })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch quiz question')
      }
      
      const questionData: QuizQuestion = await response.json()
      setCurrentQuestion(questionData)
      setSelectedIndex(null)
      setGameState('playing')
    } catch (err) {
      console.error('Error fetching quiz question:', err)
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
      setGameState('error')
    }
  }

  const handleAnswer = (selectedIdx: number, isCorrect: boolean) => {
    setSelectedIndex(selectedIdx)
    setTotalQuestions(prev => prev + 1)
    
    if (isCorrect) {
      setScore(prev => prev + 1)
      setStreak(prev => prev + 1)
    } else {
      setStreak(0)
    }
    
    setGameState('answered')
  }

  const handleNextQuestion = () => {
    fetchNewQuestion()
  }

  const handleRestart = () => {
    setScore(0)
    setTotalQuestions(0)
    setStreak(0)
    fetchNewQuestion()
  }

  useEffect(() => {
    fetchNewQuestion()
  }, [])

  if (gameState === 'loading') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          <Card className="w-full max-w-md">
            <CardContent className="p-8 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-lg">Loading quiz question...</p>
              <p className="text-sm text-muted-foreground mt-2">
                Fetching a Steam review and game options
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (gameState === 'error') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-center text-red-600">Error</CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="mb-4">{error}</p>
              <Button onClick={fetchNewQuestion}>Try Again</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (!currentQuestion) {
    return null
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-2">Steam Quiz</h1>
        <p className="text-lg text-muted-foreground">
          Guess the game from the Steam review!
        </p>
      </div>

      <ScoreDisplay 
        currentScore={score}
        totalQuestions={totalQuestions}
        streak={streak}
      />

      <div className="space-y-8">
        <ReviewDisplay
          review={currentQuestion.question.review}
          authorPlaytime={currentQuestion.question.author_playtime}
          recommended={currentQuestion.question.recommended}
          helpfulVotes={currentQuestion.question.helpful_votes}
        />

        <GameOptions
          options={currentQuestion.options}
          correctAnswerIndex={currentQuestion.correctAnswerIndex}
          onAnswer={handleAnswer}
          showResult={gameState === 'answered'}
          selectedIndex={selectedIndex}
        />

        {gameState === 'answered' && (
          <div className="flex flex-col items-center space-y-4">
            <Card className="w-full max-w-md">
              <CardContent className="p-4 text-center">
                {selectedIndex === currentQuestion.correctAnswerIndex ? (
                  <div className="text-green-600 dark:text-green-400">
                    <div className="text-2xl mb-2">🎉</div>
                    <div className="font-semibold text-lg">Correct!</div>
                    <div className="text-sm">
                      The answer was "{currentQuestion.correctGame.title}"
                    </div>
                  </div>
                ) : (
                  <div className="text-red-600 dark:text-red-400">
                    <div className="text-2xl mb-2">❌</div>
                    <div className="font-semibold text-lg">Incorrect!</div>
                    <div className="text-sm">
                      The correct answer was "{currentQuestion.correctGame.title}"
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
            
            <div className="flex gap-4">
              <Button onClick={handleNextQuestion} size="lg">
                Next Question
              </Button>
              <Button onClick={handleRestart} variant="outline" size="lg">
                Restart Quiz
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
