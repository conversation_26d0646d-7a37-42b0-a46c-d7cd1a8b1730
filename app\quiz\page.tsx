"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ReviewDisplay } from "@/components/quiz/review-display"
import { GameOptions } from "@/components/quiz/game-options"

import { QuizHints } from "@/components/quiz/quiz-hints"
import { TopBar } from "@/components/layout/top-bar"

interface QuizQuestion {
  question: {
    review: string
    author_playtime: number
    recommended: boolean
    helpful_votes: number
    funny_votes: number
    review_date: string
    author_reviews: number
    author_games: number
  }
  options: Array<{
    appid: string
    title: string
    header_image: string | null
  }>
  correctAnswerIndex: number
  correctGame: {
    appid: string
    title: string
    header_image: string | null
  }
}

type GameState = 'loading' | 'playing' | 'answered' | 'error'

export default function QuizPage() {
  const [gameState, setGameState] = useState<GameState>('loading')
  const [currentQuestion, setCurrentQuestion] = useState<QuizQuestion | null>(null)
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null)
  const [score, setScore] = useState(0)
  const [totalQuestions, setTotalQuestions] = useState(0)
  const [streak, setStreak] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [currentLanguage, setCurrentLanguage] = useState('english')

  const fetchNewQuestion = async () => {
    setGameState('loading')
    setError(null)

    try {
      // Detect browser language and map to Steam language codes
      const browserLang = navigator.language.toLowerCase()
      let steamLanguage = 'english' // default

      // Map common browser languages to Steam language codes
      const languageMap: { [key: string]: string } = {
        'it': 'italian',
        'it-it': 'italian',
        'en': 'english',
        'en-us': 'english',
        'en-gb': 'english',
        'fr': 'french',
        'fr-fr': 'french',
        'de': 'german',
        'de-de': 'german',
        'es': 'spanish',
        'es-es': 'spanish',
        'pt': 'portuguese',
        'pt-br': 'portuguese',
        'ru': 'russian',
        'ja': 'japanese',
        'ko': 'koreana',
        'zh': 'schinese',
        'zh-cn': 'schinese',
        'zh-tw': 'tchinese'
      }

      steamLanguage = languageMap[browserLang] || languageMap[browserLang.split('-')[0]] || 'english'
      setCurrentLanguage(steamLanguage)

      const response = await fetch(`/api/quiz?language=${steamLanguage}`, { cache: 'no-store' })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch quiz question')
      }
      
      const questionData: QuizQuestion = await response.json()
      setCurrentQuestion(questionData)
      setSelectedIndex(null)
      setGameState('playing')
    } catch (err) {
      console.error('Error fetching quiz question:', err)
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
      setGameState('error')
    }
  }

  const handleAnswer = (selectedIdx: number, isCorrect: boolean) => {
    setSelectedIndex(selectedIdx)
    setTotalQuestions(prev => prev + 1)
    
    if (isCorrect) {
      setScore(prev => prev + 1)
      setStreak(prev => prev + 1)
    } else {
      setStreak(0)
    }
    
    setGameState('answered')
  }

  const handleNextQuestion = () => {
    fetchNewQuestion()
  }

  const handleRestart = () => {
    setScore(0)
    setTotalQuestions(0)
    setStreak(0)
    fetchNewQuestion()
  }

  useEffect(() => {
    fetchNewQuestion()
  }, [])

  if (gameState === 'loading') {
    return (
      <div className="min-h-screen bg-steam-bg-primary">
        <TopBar showScores={true} currentScore={score} totalQuestions={totalQuestions} streak={streak} />
        <div className="bg-steam-bg-secondary/30 min-h-[calc(100vh-80px)]">
          <div className="container mx-auto px-4 py-8">
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <Card className="w-full max-w-md bg-steam-card border-steam-border">
                <CardContent className="p-8 text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-steam-accent-primary mx-auto mb-4"></div>
                  <p className="text-lg text-steam-text-primary">Loading quiz question...</p>
                  <p className="text-sm text-steam-text-secondary mt-2">
                    Fetching a Steam review and game options
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (gameState === 'error') {
    return (
      <div className="min-h-screen bg-steam-bg-primary">
        <TopBar showScores={true} currentScore={score} totalQuestions={totalQuestions} streak={streak} />
        <div className="bg-steam-bg-secondary/30 min-h-[calc(100vh-80px)]">
          <div className="container mx-auto px-4 py-8">
            <div className="flex flex-col items-center justify-center min-h-[60vh]">
              <Card className="w-full max-w-md bg-steam-card border-steam-border">
                <CardHeader>
                  <CardTitle className="text-center text-steam-error">Error</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="mb-4 text-steam-text-secondary">{error}</p>
                  <Button onClick={fetchNewQuestion} className="bg-steam-accent-primary hover:bg-steam-accent-secondary text-steam-bg-primary">
                    Try Again
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!currentQuestion) {
    return null
  }

  return (
    <div className="min-h-screen bg-steam-bg-primary">
      <TopBar showScores={true} currentScore={score} totalQuestions={totalQuestions} streak={streak} />
      <div className="bg-steam-bg-secondary/30 min-h-[calc(100vh-80px)]">
        <div className="container mx-auto px-4 py-8 max-w-6xl">

      <div className="space-y-8">
        <ReviewDisplay
          review={currentQuestion.question.review}
          authorPlaytime={currentQuestion.question.author_playtime}
          recommended={currentQuestion.question.recommended}
          helpfulVotes={currentQuestion.question.helpful_votes}
          funnyVotes={currentQuestion.question.funny_votes}
          reviewDate={currentQuestion.question.review_date}
          authorReviews={currentQuestion.question.author_reviews}
          authorGames={currentQuestion.question.author_games}
        />

        {/* Quiz Hints - Show right after the review for better navigation */}
        {gameState === 'playing' && (
          <QuizHints
            correctGameAppId={currentQuestion.correctGame.appid}
            disabled={false}
            language={currentLanguage}
            currentReview={currentQuestion.question.review}
            gameName={currentQuestion.correctGame.title}
          />
        )}

        <GameOptions
          options={currentQuestion.options}
          correctAnswerIndex={currentQuestion.correctAnswerIndex}
          onAnswer={handleAnswer}
          showResult={gameState === 'answered'}
          selectedIndex={selectedIndex}
        />

        {gameState === 'answered' && (
          <div className="flex flex-col items-center space-y-4">
            <Card className="w-full max-w-md bg-steam-card border-steam-border">
              <CardContent className="p-4 text-center">
                {selectedIndex === currentQuestion.correctAnswerIndex ? (
                  <div className="text-steam-success">
                    <div className="text-2xl mb-2">🎉</div>
                    <div className="font-semibold text-lg">Correct!</div>
                    <div className="text-sm text-steam-text-secondary">
                      The answer was "{currentQuestion.correctGame.title}"
                    </div>
                  </div>
                ) : (
                  <div className="text-steam-error">
                    <div className="text-2xl mb-2">❌</div>
                    <div className="font-semibold text-lg">Incorrect!</div>
                    <div className="text-sm text-steam-text-secondary">
                      The correct answer was "{currentQuestion.correctGame.title}"
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="flex gap-4">
              <Button onClick={handleNextQuestion} size="lg" className="bg-steam-accent-primary hover:bg-steam-accent-secondary text-steam-bg-primary">
                Next Question
              </Button>
              <Button onClick={handleRestart} variant="outline" size="lg" className="border-steam-border text-steam-text-secondary hover:bg-steam-card">
                Restart Quiz
              </Button>
            </div>
          </div>
        )}
      </div>
      </div>
    </div>
  )
}
