import { Card, CardContent } from "@/components/ui/card"

interface ScoreDisplayProps {
  currentScore: number
  totalQuestions: number
  streak: number
}

export function ScoreDisplay({ currentScore, totalQuestions, streak }: ScoreDisplayProps) {
  const percentage = totalQuestions > 0 ? Math.round((currentScore / totalQuestions) * 100) : 0

  return (
    <Card className="w-full max-w-md mx-auto mb-6 bg-steam-bg-secondary border-steam-border">
      <CardContent className="p-4">
        <div className="flex justify-between items-center">
          <div className="text-center">
            <div className="text-2xl font-bold text-steam-accent-primary">
              {currentScore}/{totalQuestions}
            </div>
            <div className="text-sm text-steam-text-secondary">Score</div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-steam-success">
              {percentage}%
            </div>
            <div className="text-sm text-steam-text-secondary">Accuracy</div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-steam-accent-secondary">
              {streak}
            </div>
            <div className="text-sm text-steam-text-secondary">Streak</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
