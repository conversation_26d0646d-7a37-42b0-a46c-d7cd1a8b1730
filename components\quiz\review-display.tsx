import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"

interface ReviewDisplayProps {
  review: string
  authorPlaytime: number
  recommended: boolean
  helpfulVotes: number
  funnyVotes: number
  reviewDate: string
  authorReviews: number
  authorGames: number
}

export function ReviewDisplay({
  review,
  authorPlaytime,
  recommended,
  helpfulVotes,
  funnyVotes,
  reviewDate,
  authorReviews,
  authorGames
}: ReviewDisplayProps) {
  const formatPlaytime = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    if (hours < 1) {
      return `${minutes} minutes`
    } else if (hours < 100) {
      return `${hours.toFixed(1)} hours`
    } else {
      return `${Math.floor(hours)} hours`
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto bg-steam-card border-steam-border">
      <CardContent className="p-6">
        {/* Steam Review Header */}
        <div className="flex items-start gap-4 mb-4">
          {/* Recommendation Thumb */}
          <div className="flex-shrink-0">
            <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
              recommended
                ? 'bg-steam-accent-primary'
                : 'bg-steam-error'
            }`}>
              <span className="text-white text-xl">
                {recommended ? '👍' : '👎'}
              </span>
            </div>
          </div>

          {/* Review Header Info */}
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <span className={`font-semibold ${
                recommended ? 'text-steam-accent-primary' : 'text-steam-error'
              }`}>
                {recommended ? 'Consigliato' : 'Non consigliato'}
              </span>
            </div>

            <div className="text-sm text-steam-text-secondary">
              {formatPlaytime(authorPlaytime)} ore in totale
            </div>

            <div className="text-xs text-steam-text-secondary mt-1">
              PUBBLICAZIONE: {reviewDate}
            </div>
          </div>
        </div>

        {/* Review Text */}
        <div className="mb-4">
          <p className="text-steam-text-primary leading-relaxed whitespace-pre-wrap">
            {review}
          </p>
        </div>

        {/* Review Footer */}
        <div className="border-t border-steam-border pt-4">
          <div className="text-sm text-steam-text-secondary mb-2">
            Questa recensione ti è stata utile?
          </div>

          <div className="flex items-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <span className="text-steam-accent-primary">👍</span>
              <span className="text-steam-text-secondary">Sì</span>
              <span className="text-steam-text-secondary">😄</span>
              <span className="text-steam-text-secondary">No</span>
              <span className="text-steam-text-secondary">😆</span>
              <span className="text-steam-accent-secondary">Divertente</span>
            </div>
          </div>

          <div className="text-xs text-steam-text-secondary mt-2">
            {helpfulVotes} persone hanno trovato utile questa recensione
            {funnyVotes > 0 && ` • ${funnyVotes} persone hanno trovato questa recensione divertente`}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
