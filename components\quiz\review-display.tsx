import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"

interface ReviewDisplayProps {
  review: string
  authorPlaytime: number
  recommended: boolean
  helpfulVotes: number
}

export function ReviewDisplay({ 
  review, 
  authorPlaytime, 
  recommended, 
  helpfulVotes 
}: ReviewDisplayProps) {
  const formatPlaytime = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    if (hours < 1) {
      return `${minutes} minutes`
    } else if (hours < 100) {
      return `${hours.toFixed(1)} hours`
    } else {
      return `${Math.floor(hours)} hours`
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto bg-steam-card border-steam-border">
      <CardHeader>
        <CardTitle className="flex flex-col sm:flex-row sm:items-center gap-4">
          <span className="text-lg text-steam-text-primary">Steam Review</span>
          <div className="flex flex-wrap items-center gap-4 text-sm">
            <span className={`px-3 py-1 rounded text-xs font-medium ${
              recommended
                ? 'bg-steam-success/20 text-steam-success border border-steam-success/30'
                : 'bg-steam-error/20 text-steam-error border border-steam-error/30'
            }`}>
              {recommended ? '👍 Recommended' : '👎 Not Recommended'}
            </span>
            <span className="text-steam-text-secondary">⏱️ {formatPlaytime(authorPlaytime)} played</span>
            <span className="text-steam-text-secondary">👍 {helpfulVotes} helpful</span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="prose prose-sm max-w-none">
          <p className="text-base leading-relaxed whitespace-pre-wrap text-steam-text-primary">
            "{review}"
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
