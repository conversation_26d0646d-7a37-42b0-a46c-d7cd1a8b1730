import Link from "next/link"

interface TopBarProps {
  currentScore?: number
  totalQuestions?: number
  streak?: number
  showScores?: boolean
}

export function TopBar({ currentScore = 0, totalQuestions = 0, streak = 0, showScores = false }: TopBarProps) {
  return (
    <div className="bg-steam-bg-secondary border-b border-steam-border sticky top-0 z-50">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo and Title */}
          <Link href="/" className="flex items-center gap-3 hover:opacity-80 transition-opacity">
            <div className="w-10 h-10 bg-steam-accent-primary rounded-lg flex items-center justify-center">
              <span className="text-steam-bg-primary font-bold text-lg">🎮</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-steam-text-primary">Steam Quiz</h1>
              <p className="text-xs text-steam-text-secondary">Guess the game from reviews</p>
            </div>
          </Link>

          {/* Scores - Only show when playing quiz */}
          {showScores && (
            <div className="flex items-center gap-6">
              <div className="text-center">
                <div className="text-lg font-bold text-steam-accent-primary">
                  {currentScore}/{totalQuestions}
                </div>
                <div className="text-xs text-steam-text-secondary">Score</div>
              </div>
              
              <div className="text-center">
                <div className="text-lg font-bold text-steam-success">
                  {totalQuestions > 0 ? Math.round((currentScore / totalQuestions) * 100) : 0}%
                </div>
                <div className="text-xs text-steam-text-secondary">Accuracy</div>
              </div>
              
              <div className="text-center">
                <div className="text-lg font-bold text-steam-accent-secondary">
                  {streak}
                </div>
                <div className="text-xs text-steam-text-secondary">Streak</div>
              </div>
            </div>
          )}

          {/* Navigation Links */}
          <div className="flex items-center gap-4">
            <Link 
              href="/quiz" 
              className="px-4 py-2 bg-steam-accent-primary hover:bg-steam-accent-secondary text-steam-bg-primary font-medium rounded-lg transition-colors"
            >
              Play Quiz
            </Link>
            <Link 
              href="/test-db" 
              className="px-4 py-2 border border-steam-border text-steam-text-secondary hover:bg-steam-card rounded-lg transition-colors"
            >
              Test DB
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
