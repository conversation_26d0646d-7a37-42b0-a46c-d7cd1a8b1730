"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ReviewDisplay } from "./review-display"

interface QuizHintsProps {
  correctGameAppId: string
  disabled: boolean
  language: string
  currentReview: string
}

interface Screenshot {
  screenshot_url: string
  thumbnail_url: string
}

interface GameDetails {
  name: string
  release_year: string
  release_date: string
  price: string
  is_free: boolean
  genres: string[]
  developers: string[]
  publishers: string[]
}

interface SecondReview {
  review: string
  author_playtime: number
  recommended: boolean
  helpful_votes: number
  funny_votes: number
  review_date: string
  author_reviews: number
  author_games: number
}

export function QuizHints({ correctGameAppId, disabled, language, currentReview }: QuizHintsProps) {
  const [screenshot, setScreenshot] = useState<Screenshot | null>(null)
  const [gameDetails, setGameDetails] = useState<GameDetails | null>(null)
  const [secondReview, setSecondReview] = useState<SecondReview | null>(null)
  const [loadingStates, setLoadingStates] = useState({
    screenshot: false,
    details: false,
    review: false
  })
  const [usedHints, setUsedHints] = useState({
    screenshot: false,
    details: false,
    review: false
  })

  const fetchScreenshot = async () => {
    if (usedHints.screenshot || disabled) return
    
    setLoadingStates(prev => ({ ...prev, screenshot: true }))
    try {
      const response = await fetch(`/api/steam-screenshot?appid=${correctGameAppId}`)
      if (response.ok) {
        const data = await response.json()
        setScreenshot(data)
        setUsedHints(prev => ({ ...prev, screenshot: true }))
      }
    } catch (error) {
      console.error('Error fetching screenshot:', error)
    } finally {
      setLoadingStates(prev => ({ ...prev, screenshot: false }))
    }
  }

  const fetchGameDetails = async () => {
    if (usedHints.details || disabled) return
    
    setLoadingStates(prev => ({ ...prev, details: true }))
    try {
      const response = await fetch(`/api/game-details?appid=${correctGameAppId}`)
      if (response.ok) {
        const data = await response.json()
        setGameDetails(data)
        setUsedHints(prev => ({ ...prev, details: true }))
      }
    } catch (error) {
      console.error('Error fetching game details:', error)
    } finally {
      setLoadingStates(prev => ({ ...prev, details: false }))
    }
  }

  const fetchSecondReview = async () => {
    if (usedHints.review || disabled) return

    setLoadingStates(prev => ({ ...prev, review: true }))
    try {
      const excludeParam = encodeURIComponent(currentReview)
      const response = await fetch(`/api/steam-reviews?appid=${correctGameAppId}&language=${language}&exclude=${excludeParam}`)
      if (response.ok) {
        const data = await response.json()
        setSecondReview(data)
        setUsedHints(prev => ({ ...prev, review: true }))
      }
    } catch (error) {
      console.error('Error fetching second review:', error)
    } finally {
      setLoadingStates(prev => ({ ...prev, review: false }))
    }
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card className="bg-steam-bg-secondary border-steam-border">
        <CardHeader>
          <CardTitle className="text-steam-text-primary text-center">
            💡 Aiuti Disponibili
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Screenshot Hint */}
            <Button
              onClick={fetchScreenshot}
              disabled={disabled || usedHints.screenshot || loadingStates.screenshot}
              className={`h-auto p-4 flex flex-col items-center gap-2 ${
                usedHints.screenshot 
                  ? 'bg-steam-success/20 border-steam-success text-steam-success' 
                  : 'bg-steam-card hover:bg-steam-accent-primary/10 border-steam-border text-steam-text-primary'
              }`}
              variant="outline"
            >
              <span className="text-2xl">🖼️</span>
              <span className="text-sm font-medium">Screenshot</span>
              <span className="text-xs text-center">
                {usedHints.screenshot ? 'Usato' : 'Mostra uno screenshot del gioco'}
              </span>
            </Button>

            {/* Game Details Hint */}
            <Button
              onClick={fetchGameDetails}
              disabled={disabled || usedHints.details || loadingStates.details}
              className={`h-auto p-4 flex flex-col items-center gap-2 ${
                usedHints.details 
                  ? 'bg-steam-success/20 border-steam-success text-steam-success' 
                  : 'bg-steam-card hover:bg-steam-accent-primary/10 border-steam-border text-steam-text-primary'
              }`}
              variant="outline"
            >
              <span className="text-2xl">📊</span>
              <span className="text-sm font-medium">Dati Gioco</span>
              <span className="text-xs text-center">
                {usedHints.details ? 'Usato' : 'Anno e prezzo del gioco'}
              </span>
            </Button>

            {/* Second Review Hint */}
            <Button
              onClick={fetchSecondReview}
              disabled={disabled || usedHints.review || loadingStates.review}
              className={`h-auto p-4 flex flex-col items-center gap-2 ${
                usedHints.review 
                  ? 'bg-steam-success/20 border-steam-success text-steam-success' 
                  : 'bg-steam-card hover:bg-steam-accent-primary/10 border-steam-border text-steam-text-primary'
              }`}
              variant="outline"
            >
              <span className="text-2xl">📝</span>
              <span className="text-sm font-medium">Seconda Recensione</span>
              <span className="text-xs text-center">
                {usedHints.review ? 'Usato' : 'Mostra un\'altra recensione'}
              </span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Screenshot Display */}
      {screenshot && (
        <Card className="bg-steam-card border-steam-border">
          <CardHeader>
            <CardTitle className="text-steam-text-primary flex items-center gap-2">
              🖼️ Screenshot del Gioco
            </CardTitle>
          </CardHeader>
          <CardContent>
            <img
              src={screenshot.screenshot_url}
              alt="Game Screenshot"
              className="w-full rounded-lg shadow-lg"
              onError={(e) => {
                e.currentTarget.src = screenshot.thumbnail_url
              }}
            />
          </CardContent>
        </Card>
      )}

      {/* Game Details Display */}
      {gameDetails && (
        <Card className="bg-steam-card border-steam-border">
          <CardHeader>
            <CardTitle className="text-steam-text-primary flex items-center gap-2">
              📊 Informazioni Gioco
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-steam-text-secondary text-sm">Anno di Pubblicazione:</span>
                <div className="text-steam-text-primary font-medium">{gameDetails.release_year}</div>
              </div>
              <div>
                <span className="text-steam-text-secondary text-sm">Prezzo:</span>
                <div className="text-steam-text-primary font-medium">{gameDetails.price}</div>
              </div>
              {gameDetails.genres.length > 0 && (
                <div className="md:col-span-2">
                  <span className="text-steam-text-secondary text-sm">Generi:</span>
                  <div className="text-steam-text-primary">{gameDetails.genres.join(', ')}</div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Second Review Display */}
      {secondReview && (
        <div>
          <h3 className="text-steam-text-primary text-lg font-semibold mb-4 text-center">
            📝 Seconda Recensione
          </h3>
          <ReviewDisplay
            review={secondReview.review}
            authorPlaytime={secondReview.author_playtime}
            recommended={secondReview.recommended}
            helpfulVotes={secondReview.helpful_votes}
            funnyVotes={secondReview.funny_votes}
            reviewDate={secondReview.review_date}
            authorReviews={secondReview.author_reviews}
            authorGames={secondReview.author_games}
          />
        </div>
      )}
    </div>
  )
}
